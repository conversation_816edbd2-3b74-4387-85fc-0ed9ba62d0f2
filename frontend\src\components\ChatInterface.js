import React, { useState, useRef, useEffect } from 'react';
import Message from './Message';
import { chatAPI } from '../services/api';
import { Send, Database, Bot } from 'lucide-react';

const ChatInterface = ({ 
  conversationId, 
  messages, 
  onNewMessage, 
  onConversationCreate,
  user 
}) => {
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [useStreaming, setUseStreaming] = useState(false);
  const [activeTab, setActiveTab] = useState('knowledge'); // 'knowledge' or 'agent'
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const messageToSend = inputMessage.trim();
    setInputMessage('');
    setIsLoading(true);

    // Add user message immediately
    const userMessage = {
      role: 'user',
      content: messageToSend,
      timestamp: new Date().toISOString()
    };
    onNewMessage(userMessage);

    try {
      const useAgent = activeTab === 'agent';
      const useRAG = activeTab === 'knowledge';

      if (useStreaming) {
        await handleStreamingResponse(messageToSend, useRAG);
      } else if (useAgent) {
        await handleAgentResponse(messageToSend);
      } else {
        await handleRegularResponse(messageToSend, useRAG);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage = {
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date().toISOString()
      };
      onNewMessage(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegularResponse = async (message, useRAG) => {
    const response = await chatAPI.sendMessage({
      user_id: user.id,
      conversation_id: conversationId,
      message: message,
      use_rag: useRAG,
      use_agent: activeTab === 'agent'
    });

    const assistantMessage = {
      role: 'assistant',
      content: response.data.response,
      timestamp: new Date().toISOString(),
      metadata: {
        used_agent: response.data.used_agent,
        used_rag: response.data.used_rag
      }
    };
    onNewMessage(assistantMessage);

    if (!conversationId && response.data.conversation_id) {
      onConversationCreate(response.data.conversation_id);
    }
  };

  const handleAgentResponse = async (message) => {
    const response = await chatAPI.sendAgentMessage({
      user_id: user.id,
      conversation_id: conversationId,
      message: message,
      tools: ['agent']
    });

    const assistantMessage = {
      role: 'assistant',
      content: response.data.response,
      timestamp: new Date().toISOString(),
      metadata: {
        used_agent: true,
        tools_used: response.data.tools_used
      }
    };
    onNewMessage(assistantMessage);

    if (!conversationId && response.data.conversation_id) {
      onConversationCreate(response.data.conversation_id);
    }
  };

  const handleStreamingResponse = async (message, useRAG) => {
    let fullResponse = '';
    const assistantMessage = {
      role: 'assistant',
      content: '',
      timestamp: new Date().toISOString(),
      metadata: {
        used_rag: useRAG,
        streaming: true
      }
    };
    
    onNewMessage(assistantMessage);
    const messageIndex = messages.length;

    try {
      const response = await fetch('http://localhost:5000/api/chat/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: user.id,
          conversation_id: conversationId,
          message: message,
          use_rag: useRAG,
        }),
      });

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = JSON.parse(line.slice(6));
            
            if (data.chunk) {
              fullResponse += data.chunk;
              onNewMessage({
                ...assistantMessage,
                content: fullResponse
              }, messageIndex);
            }
            
            if (data.complete) {
              if (!conversationId) {
                // Handle new conversation creation if needed
              }
              return;
            }
          }
        }
      }
    } catch (error) {
      console.error('Streaming error:', error);
      onNewMessage({
        ...assistantMessage,
        content: 'Error: Failed to get streaming response.'
      }, messageIndex);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="flex-1 flex flex-col h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 p-6 shadow-sm">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="text-2xl animate-pulse-slow">🧠</div>
            <div>
              <h3 className="text-2xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                Sozhaa Tech AI
              </h3>
              <p className="text-sm text-gray-600 font-medium">Powered by Advanced AI</p>
            </div>
          </div>

          {/* Streaming Toggle */}
          <label className="flex items-center gap-2 px-4 py-2 bg-primary-50 hover:bg-primary-100 rounded-lg cursor-pointer transition-colors">
            <input
              type="checkbox"
              checked={useStreaming}
              onChange={(e) => setUseStreaming(e.target.checked)}
              className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
            />
            <span className="text-sm font-medium text-primary-700">Use Streaming</span>
          </label>
        </div>

        {/* Tabs */}
        <div className="flex gap-2">
          <button
            onClick={() => setActiveTab('knowledge')}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all ${
              activeTab === 'knowledge'
                ? 'bg-primary-500 text-white shadow-md'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            <Database size={18} />
            Knowledge Base
          </button>
          <button
            onClick={() => setActiveTab('agent')}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all ${
              activeTab === 'agent'
                ? 'bg-primary-500 text-white shadow-md'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            <Bot size={18} />
            AI Agent
          </button>
        </div>
      </div>

      {/* Messages Container */}
      <div className="flex-1 overflow-y-auto custom-scrollbar p-6">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="text-6xl mb-6 animate-bounce-slow">🚀</div>
            <h2 className="text-3xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent mb-4">
              Welcome to Sozhaa Tech AI
            </h2>
            <p className="text-lg text-gray-600 mb-8 max-w-2xl">
              Your intelligent conversation partner powered by advanced AI technology.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl">
              <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                <div className="text-2xl mb-3">🤖</div>
                <h3 className="font-bold text-gray-800 mb-2">LangChain Powered</h3>
                <p className="text-gray-600 text-sm">Advanced AI with memory and tools</p>
              </div>
              <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                <div className="text-2xl mb-3">🔍</div>
                <h3 className="font-bold text-gray-800 mb-2">RAG Enabled</h3>
                <p className="text-gray-600 text-sm">Search your knowledge base</p>
              </div>
              <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                <div className="text-2xl mb-3">🛠️</div>
                <h3 className="font-bold text-gray-800 mb-2">Agent Tools</h3>
                <p className="text-gray-600 text-sm">Web search, calculations, and more</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="max-w-4xl mx-auto space-y-6">
            {messages.map((message, index) => (
              <Message
                key={index}
                message={message}
                isUser={message.role === 'user'}
              />
            ))}
            <div ref={messagesEndRef} />
            {isLoading && (
              <div className="flex justify-center">
                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-4 shadow-sm border border-gray-200">
                  <div className="flex items-center gap-3">
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-primary-500 rounded-full typing-dot"></div>
                      <div className="w-2 h-2 bg-primary-500 rounded-full typing-dot"></div>
                      <div className="w-2 h-2 bg-primary-500 rounded-full typing-dot"></div>
                    </div>
                    {activeTab === 'agent' && (
                      <span className="text-sm text-gray-600 font-medium">Agent is thinking and using tools...</span>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Input Area */}
      <div className="bg-white/80 backdrop-blur-sm border-t border-gray-200 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex gap-4 items-end">
            <div className="flex-1 relative">
              <textarea
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Type your message here..."
                rows="1"
                disabled={isLoading}
                className="w-full px-6 py-4 bg-white border border-gray-300 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent shadow-sm transition-all duration-200 text-gray-800 placeholder-gray-500"
                style={{ minHeight: '56px', maxHeight: '150px' }}
              />
            </div>
            <button
              onClick={handleSendMessage}
              disabled={isLoading || !inputMessage.trim()}
              className="px-6 py-4 bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 disabled:from-gray-300 disabled:to-gray-400 text-white rounded-2xl font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg disabled:hover:scale-100 disabled:hover:shadow-none flex items-center gap-2 min-w-[120px] justify-center"
            >
              {isLoading ? (
                <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
              ) : (
                <>
                  <Send size={20} />
                  Send
                </>
              )}
            </button>
          </div>

          {/* Active Tools Indicator */}
          <div className="flex gap-2 justify-center mt-4">
            {activeTab === 'knowledge' && (
              <span className="px-3 py-1 bg-gradient-to-r from-primary-500 to-secondary-500 text-white text-xs font-medium rounded-full shadow-sm">
                Knowledge Base Active
              </span>
            )}
            {activeTab === 'agent' && (
              <span className="px-3 py-1 bg-gradient-to-r from-primary-500 to-secondary-500 text-white text-xs font-medium rounded-full shadow-sm">
                AI Agent Active
              </span>
            )}
            {useStreaming && (
              <span className="px-3 py-1 bg-green-500 text-white text-xs font-medium rounded-full shadow-sm">
                Streaming Enabled
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;