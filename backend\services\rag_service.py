import chromadb
from sentence_transformers import SentenceTransformer
import uuid
import os
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import TextLoader, PyPDFLoader, WebBaseLoader
from langchain_chroma import Chroma
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain.schema import Document
from dotenv import load_dotenv

load_dotenv()

class RAGService:
    def __init__(self):
        self.client = chromadb.PersistentClient(path="./chroma_db")
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
        
        # LangChain Chroma setup
        self.embeddings = GoogleGenerativeAIEmbeddings(
            model="models/embedding-001",
            google_api_key=os.getenv('GEMINI_API_KEY')
        )
        
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len
        )
        
        # Create or get collection
        self.collection = self.client.get_or_create_collection(
            name="knowledge_base",
            metadata={"description": "Custom knowledge base for RAG"}
        )
        
        # LangChain vector store
        self.vector_store = Chroma(
            collection_name="knowledge_base",
            embedding_function=self.embeddings,
            persist_directory="./chroma_db"
        )
    
    def load_documents(self, file_paths):
        """Load documents using LangChain document loaders"""
        documents = []
        for file_path in file_paths:
            if file_path.endswith('.pdf'):
                loader = PyPDFLoader(file_path)
            elif file_path.startswith('http'):
                loader = WebBaseLoader(file_path)
            else:
                loader = TextLoader(file_path)
            loaded_docs = loader.load()
            documents.extend(loaded_docs)
        
        return documents
    
    def add_documents(self, documents, metadatas=None, ids=None):
        """Add documents to the knowledge base using LangChain"""
        if not ids:
            ids = [str(uuid.uuid4()) for _ in documents]
        
        # Split documents
        split_docs = self.text_splitter.split_documents(documents)
        
        # Add to both ChromaDB and LangChain vector store
        self.vector_store.add_documents(split_docs)
        
        # Also add to direct ChromaDB for compatibility
        texts = [doc.page_content for doc in split_docs]
        embeddings = self.embedding_model.encode(texts).tolist()
        
        self.collection.add(
            embeddings=embeddings,
            documents=texts,
            metadatas=metadatas or [{} for _ in texts],
            ids=ids[:len(texts)]
        )
    
    def add_texts(self, texts, metadatas=None):
        """Add raw texts to knowledge base"""
        documents = [Document(page_content=text) for text in texts]
        self.add_documents(documents, metadatas)
    
    def search_similar(self, query, n_results=3):
        """Search for similar documents using LangChain"""
        # Using LangChain similarity search
        results = self.vector_store.similarity_search(query, k=n_results)
        
        return {
            'documents': [[doc.page_content for doc in results]],
            'metadatas': [[doc.metadata for doc in results]]
        }
    
    def get_context(self, query, n_results=3):
        """Get relevant context for RAG using LangChain"""
        similar_docs = self.search_similar(query, n_results)
        
        if similar_docs['documents']:
            context = "\n\n".join(similar_docs['documents'][0])
            return context
        return ""
    
    def create_retrieval_chain(self, llm):
        """Create a retrieval chain for RAG"""
        from langchain.chains import RetrievalQA
        
        retriever = self.vector_store.as_retriever(
            search_type="similarity",
            search_kwargs={"k": 3}
        )
        
        return RetrievalQA.from_chain_type(
            llm=llm,
            chain_type="stuff",
            retriever=retriever,
            return_source_documents=True
        )
    
    def rag_query(self, query, llm):
        """Execute RAG query using LangChain"""
        qa_chain = self.create_retrieval_chain(llm)
        result = qa_chain({"query": query})
        return result