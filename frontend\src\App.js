import React, { useState, useEffect } from 'react';
import Sidebar from './components/Sidebar';
import ChatInterface from './components/ChatInterface';
import Login from './components/Login';
import { chatAPI, authAPI } from './services/api';

function App() {
  const [user, setUser] = useState(null);
  const [conversations, setConversations] = useState([]);
  const [currentConversation, setCurrentConversation] = useState(null);
  const [messages, setMessages] = useState([]);

  useEffect(() => {
    // Check if user is logged in (simple localStorage check)
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      const userData = JSON.parse(savedUser);
      setUser(userData);
      loadConversations(userData.id);
    }
  }, []);

  const loadConversations = async (userId) => {
    try {
      const response = await chatAPI.getConversations(userId);
      setConversations(response.data.conversations);
    } catch (error) {
      console.error('Error loading conversations:', error);
    }
  };

  const handleLogin = async (username, email) => {
    try {
      const response = await authAPI.login({ username, email });
      const userData = { id: response.data.user_id, username, email };
      setUser(userData);
      localStorage.setItem('user', JSON.stringify(userData));
      loadConversations(userData.id);
    } catch (error) {
      console.error('Login error:', error);
      alert('Login failed. Please try again.');
    }
  };

  const handleSelectConversation = async (conversationId) => {
    setCurrentConversation(conversationId);
    try {
      const response = await chatAPI.getConversation(conversationId);
      setMessages(response.data.messages);
    } catch (error) {
      console.error('Error loading conversation:', error);
    }
  };

  const handleNewConversation = () => {
    setCurrentConversation(null);
    setMessages([]);
  };

  const handleNewMessage = (message, index = null) => {
    if (index !== null) {
      // Update existing message (for streaming)
      setMessages(prev => {
        const newMessages = [...prev];
        newMessages[index] = message;
        return newMessages;
      });
    } else {
      // Add new message
      setMessages(prev => [...prev, message]);
    }
  };

  const handleConversationCreate = (conversationId) => {
    setCurrentConversation(conversationId);
    // Reload conversations to include the new one
    loadConversations(user.id);
  };

  const handleLogout = () => {
    // Clear user session
    localStorage.removeItem('user');
    setUser(null);
    setConversations([]);
    setCurrentConversation(null);
    setMessages([]);
  };

  if (!user) {
    return <Login onLogin={handleLogin} />;
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar
        conversations={conversations}
        currentConversation={currentConversation}
        onSelectConversation={handleSelectConversation}
        onNewConversation={handleNewConversation}
        onLogout={handleLogout}
        user={user}
      />
      <ChatInterface
        conversationId={currentConversation}
        messages={messages}
        onNewMessage={handleNewMessage}
        onConversationCreate={handleConversationCreate}
        user={user}
      />
    </div>
  );
}

export default App;