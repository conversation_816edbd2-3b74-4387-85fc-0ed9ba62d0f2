import React, { useState } from 'react';

const Login = ({ onLogin }) => {
  const [username, setUsername] = useState('deepak');
  const [email, setEmail] = useState('<EMAIL>');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (username.trim() && email.trim()) {
      setIsLoading(true);
      try {
        await onLogin(username.trim(), email.trim());
      } catch (error) {
        console.error('Login error:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleDemoLogin = () => {
    setUsername('deepak');
    setEmail('<EMAIL>');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-500 via-secondary-500 to-primary-600 relative overflow-hidden">
      {/* Floating Shapes */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full animate-bounce-slow"></div>
        <div className="absolute top-60 right-10 w-32 h-32 bg-white/10 rounded-full animate-pulse-slow"></div>
        <div className="absolute bottom-20 left-20 w-16 h-16 bg-white/10 rounded-full animate-bounce"></div>
        <div className="absolute top-10 right-1/3 w-24 h-24 bg-white/10 rounded-full animate-pulse"></div>
      </div>

      <div className="relative z-10 w-full max-w-md px-6">
        <div className="bg-white/95 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-4 mb-4">
              <div className="text-5xl animate-pulse-slow">🤖</div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                Sozhaa AI
              </h1>
            </div>
            <p className="text-gray-600 text-lg font-medium">Your Intelligent Conversation Partner</p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="username" className="flex items-center gap-2 mb-2 text-gray-700 font-semibold">
                <span className="text-lg">👤</span>
                Username
              </label>
              <input
                type="text"
                id="username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Enter your username"
                required
                className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-300 bg-white/80 backdrop-blur-sm"
              />
            </div>

            <div>
              <label htmlFor="email" className="flex items-center gap-2 mb-2 text-gray-700 font-semibold">
                <span className="text-lg">📧</span>
                Email Address
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email address"
                required
                className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-300 bg-white/80 backdrop-blur-sm"
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full py-4 bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 disabled:from-gray-400 disabled:to-gray-500 text-white font-bold rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg disabled:hover:scale-100 disabled:hover:shadow-none flex items-center justify-center gap-3 text-lg"
            >
              {isLoading ? (
                <>
                  <div className="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  Connecting...
                </>
              ) : (
                <>
                  <span className="text-xl">🚀</span>
                  Start Chatting
                </>
              )}
            </button>

            <div className="text-center mt-6 pt-6 border-t border-gray-200">
              <p className="text-gray-600 text-sm mb-3">Try with demo credentials:</p>
              <button
                type="button"
                onClick={handleDemoLogin}
                className="px-6 py-2 border-2 border-primary-500 text-primary-600 hover:bg-primary-500 hover:text-white rounded-lg font-semibold transition-all duration-300 text-sm"
              >
                Use Demo Account
              </button>
            </div>
          </form>

          <div className="grid grid-cols-3 gap-4 mt-8 pt-6 border-t border-gray-200">
            <div className="text-center">
              <div className="text-2xl mb-2">💬</div>
              <span className="text-xs text-gray-600 font-medium">Smart Conversations</span>
            </div>
            <div className="text-center">
              <div className="text-2xl mb-2">🧠</div>
              <span className="text-xs text-gray-600 font-medium">AI-Powered Responses</span>
            </div>
            <div className="text-center">
              <div className="text-2xl mb-2">📚</div>
              <span className="text-xs text-gray-600 font-medium">Knowledge Base Integration</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;