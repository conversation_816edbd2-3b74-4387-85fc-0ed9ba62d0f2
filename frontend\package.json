{"name": "chatgpt-like-frontend", "version": "1.0.0", "description": "React frontend for ChatGPT-like application", "main": "index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"axios": "^1.5.0", "lucide-react": "^0.545.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.14"}}