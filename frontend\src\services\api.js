import axios from 'axios';

const API_BASE_URL = 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const chatAPI = {
  sendMessage: (data) => api.post('/chat', data),
  streamMessage: (data) => api.post('/chat/stream', data),
  sendAgentMessage: (data) => api.post('/chat/agent', data),
  getConversations: (userId) => api.get(`/conversations?user_id=${userId}`),
  getConversation: (conversationId) => api.get(`/conversation/${conversationId}`),
};

export const agentAPI = {
  getTools: () => api.get('/agent/tools'),
};

export const langchainAPI = {
  getMemory: () => api.get('/langchain/memory'),
};

export const ragAPI = {
  addDocuments: (data) => api.post('/rag/documents', data),
  searchDocuments: (data) => api.post('/rag/search', data),
  advancedRAG: (data) => api.post('/rag/advanced', data),
};

export const authAPI = {
  login: (data) => api.post('/auth/login', data),
};

export default api;