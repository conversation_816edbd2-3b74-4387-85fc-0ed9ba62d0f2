import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'lucide-react';

const Message = ({ message, isUser }) => {
  return (
    <div className={`flex gap-4 ${isUser ? 'justify-end' : 'justify-start'}`}>
      {/* Avatar */}
      {!isUser && (
        <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center shadow-md">
          <Bot size={20} className="text-white" />
        </div>
      )}

      {/* Message Content */}
      <div className={`max-w-[70%] ${isUser ? 'order-first' : ''}`}>
        <div
          className={`px-6 py-4 rounded-2xl shadow-sm ${
            isUser
              ? 'bg-gradient-to-r from-primary-500 to-secondary-500 text-white ml-auto'
              : 'bg-white border border-gray-200 text-gray-800'
          }`}
        >
          <div className="text-sm leading-relaxed whitespace-pre-wrap">
            {message.content}
          </div>

          {/* Metadata */}
          {message.metadata && (
            <div className="flex flex-wrap gap-2 mt-3 pt-3 border-t border-white/20">
              {message.metadata.used_agent && (
                <span className="px-2 py-1 bg-white/20 text-xs font-medium rounded-full">
                  Agent
                </span>
              )}
              {message.metadata.used_rag && (
                <span className="px-2 py-1 bg-white/20 text-xs font-medium rounded-full">
                  Knowledge Base
                </span>
              )}
              {message.metadata.tools_used && (
                <span className="px-2 py-1 bg-white/20 text-xs font-medium rounded-full">
                  Tools: {message.metadata.tools_used.join(', ')}
                </span>
              )}
            </div>
          )}
        </div>

        {/* Timestamp */}
        <div className={`flex items-center gap-1 mt-2 text-xs text-gray-500 ${isUser ? 'justify-end' : 'justify-start'}`}>
          <Clock size={12} />
          {new Date(message.timestamp).toLocaleTimeString()}
        </div>
      </div>

      {/* User Avatar */}
      {isUser && (
        <div className="flex-shrink-0 w-10 h-10 bg-gray-400 rounded-full flex items-center justify-center shadow-md">
          <User size={20} className="text-white" />
        </div>
      )}
    </div>
  );
};

export default Message;