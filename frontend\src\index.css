/* Modern CSS Reset and Base Styles */

/* Custom global styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Utility Classes */
.flex { display: flex; }
.flex-1 { flex: 1; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }
.justify-start { justify-content: flex-start; }
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.h-screen { height: 100vh; }
.h-full { height: 100%; }
.h-10 { height: 2.5rem; }
.w-full { width: 100%; }
.w-80 { width: 20rem; }
.w-10 { width: 2.5rem; }
.w-2 { width: 0.5rem; }
.h-2 { height: 0.5rem; }
.w-5 { width: 1.25rem; }
.h-5 { height: 1.25rem; }
.w-6 { width: 1.5rem; }
.h-6 { height: 1.5rem; }
.max-w-md { max-width: 28rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-70 { max-width: 70%; }
.min-h-screen { min-height: 100vh; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.pt-6 { padding-top: 1.5rem; }
.pb-6 { padding-bottom: 1.5rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }
.ml-auto { margin-left: auto; }
.mr-3 { margin-right: 0.75rem; }
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-5xl { font-size: 3rem; }
.text-6xl { font-size: 3.75rem; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.text-center { text-align: center; }
.text-white { color: white; }
.text-gray-500 { color: #6b7280; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1f2937; }
.text-red-400 { color: #f87171; }
.text-red-300 { color: #fca5a5; }
.bg-white { background-color: white; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-gray-300 { background-color: #d1d5db; }
.bg-gray-400 { background-color: #9ca3af; }
.bg-gray-800 { background-color: #1f2937; }
.bg-red-500 { background-color: #ef4444; }
.bg-green-500 { background-color: #10b981; }
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }
.border-t { border-top-width: 1px; }
.border-b { border-bottom-width: 1px; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-3xl { border-radius: 1.5rem; }
.rounded-full { border-radius: 9999px; }
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
.shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }
.overflow-hidden { overflow: hidden; }
.overflow-y-auto { overflow-y: auto; }
.relative { position: relative; }
.absolute { position: absolute; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.top-2 { top: 0.5rem; }
.top-8 { top: 2rem; }
.top-10 { top: 2.5rem; }
.top-20 { top: 5rem; }
.top-60 { top: 15rem; }
.right-0 { right: 0; }
.right-2 { right: 0.5rem; }
.right-10 { right: 2.5rem; }
.right-1\/3 { right: 33.333333%; }
.bottom-20 { bottom: 5rem; }
.left-10 { left: 2.5rem; }
.left-20 { left: 5rem; }
.z-10 { z-index: 10; }
.cursor-pointer { cursor: pointer; }
.transition-all { transition: all 0.3s ease; }
.transition-colors { transition: color 0.3s ease, background-color 0.3s ease; }
.transition-opacity { transition: opacity 0.3s ease; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.ease { transition-timing-function: ease; }
.opacity-0 { opacity: 0; }
.opacity-100 { opacity: 1; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.order-first { order: -9999; }
.flex-shrink-0 { flex-shrink: 0; }
.truncate { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
.whitespace-pre-wrap { white-space: pre-wrap; }
.leading-relaxed { line-height: 1.625; }
.resize-none { resize: none; }
.focus\:outline-none:focus { outline: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5); }
.focus\:border-transparent:focus { border-color: transparent; }
.hover\:bg-gray-200:hover { background-color: #e5e7eb; }
.hover\:bg-white\/10:hover { background-color: rgba(255, 255, 255, 0.1); }
.hover\:bg-white\/20:hover { background-color: rgba(255, 255, 255, 0.2); }
.hover\:bg-red-500\/20:hover { background-color: rgba(239, 68, 68, 0.2); }
.hover\:bg-red-500\/30:hover { background-color: rgba(239, 68, 68, 0.3); }
.hover\:text-red-300:hover { color: #fca5a5; }
.hover\:scale-105:hover { transform: scale(1.05); }
.hover\:shadow-md:hover { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.hover\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.disabled\:hover\:scale-100:disabled:hover { transform: scale(1); }
.disabled\:hover\:shadow-none:disabled:hover { box-shadow: none; }
.group:hover .group-hover\:opacity-100 { opacity: 1; }
.backdrop-blur-sm { backdrop-filter: blur(4px); }
.backdrop-blur-xl { backdrop-filter: blur(24px); }

/* Gradient Backgrounds */
.bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.bg-gradient-to-b { background-image: linear-gradient(to bottom, var(--tw-gradient-stops)); }

/* Primary Colors */
.from-primary-400 { --tw-gradient-from: #8b93f8; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(139, 147, 248, 0)); }
.from-primary-500 { --tw-gradient-from: #667eea; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(102, 126, 234, 0)); }
.from-primary-600 { --tw-gradient-from: #5a67d8; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(90, 103, 216, 0)); }
.to-secondary-400 { --tw-gradient-to: #e879f9; }
.to-secondary-500 { --tw-gradient-to: #764ba2; }
.to-secondary-600 { --tw-gradient-to: #c026d3; }
.to-primary-600 { --tw-gradient-to: #5a67d8; }

/* Slate Colors */
.from-slate-900 { --tw-gradient-from: #0f172a; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(15, 23, 42, 0)); }
.via-slate-800 { --tw-gradient-to: rgba(30, 41, 59, 0); --tw-gradient-stops: var(--tw-gradient-from), #1e293b, var(--tw-gradient-to, rgba(30, 41, 59, 0)); }
.to-slate-900 { --tw-gradient-to: #0f172a; }

/* Background Clip Text */
.bg-clip-text { -webkit-background-clip: text; background-clip: text; }
.text-transparent { color: transparent; }

/* Primary Color Classes */
.bg-primary-50 { background-color: #f0f4ff; }
.bg-primary-500 { background-color: #667eea; }
.text-primary-600 { color: #5a67d8; }
.text-primary-700 { color: #4c51bf; }
.border-primary-400 { border-color: #8b93f8; }
.border-primary-500 { border-color: #667eea; }

/* White with opacity */
.bg-white\/5 { background-color: rgba(255, 255, 255, 0.05); }
.bg-white\/10 { background-color: rgba(255, 255, 255, 0.1); }
.bg-white\/20 { background-color: rgba(255, 255, 255, 0.2); }
.bg-white\/80 { background-color: rgba(255, 255, 255, 0.8); }
.bg-white\/95 { background-color: rgba(255, 255, 255, 0.95); }
.border-white\/10 { border-color: rgba(255, 255, 255, 0.1); }
.border-white\/20 { border-color: rgba(255, 255, 255, 0.2); }
.border-white\/30 { border-color: rgba(255, 255, 255, 0.3); }
.text-white\/60 { color: rgba(255, 255, 255, 0.6); }
.text-white\/70 { color: rgba(255, 255, 255, 0.7); }

/* Disabled states */
.disabled\:from-gray-300:disabled { --tw-gradient-from: #d1d5db; }
.disabled\:to-gray-400:disabled { --tw-gradient-to: #9ca3af; }
.disabled\:from-gray-400:disabled { --tw-gradient-from: #9ca3af; }
.disabled\:to-gray-500:disabled { --tw-gradient-to: #6b7280; }

/* Hover states for gradients */
.hover\:from-primary-600:hover { --tw-gradient-from: #5a67d8; }
.hover\:to-secondary-600:hover { --tw-gradient-to: #c026d3; }
.hover\:bg-primary-100:hover { background-color: #e0e9ff; }
.hover\:bg-primary-500:hover { background-color: #667eea; }

/* Focus states */
.focus\:ring-primary-200:focus { box-shadow: 0 0 0 3px rgba(199, 214, 254, 0.5); }
.focus\:ring-primary-500:focus { box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.5); }
.focus\:border-primary-500:focus { border-color: #667eea; }

/* Responsive grid */
@media (min-width: 768px) {
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

/* Animation keyframes */
@keyframes pulse-slow {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes bounce-slow {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-25%); }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Animation classes */
.animate-pulse-slow { animation: pulse-slow 3s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.animate-bounce-slow { animation: bounce-slow 3s infinite; }
.animate-bounce { animation: bounce 1s infinite; }
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.animate-spin { animation: spin 1s linear infinite; }

/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* Typing animation for loading dots */
.typing-dot {
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}
