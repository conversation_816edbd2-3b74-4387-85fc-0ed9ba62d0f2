import uuid
from models.database import db, User

class AuthService:
    def __init__(self):
        pass
    
    def authenticate_user(self, username, email):
        """Simple authentication - in production, use proper auth"""
        user = User.query.filter_by(email=email).first()
        
        if not user:
            # Create new user
            user = User(
                id=str(uuid.uuid4()),
                username=username,
                email=email
            )
            db.session.add(user)
            db.session.commit()
        
        return user.id
    
    def validate_user(self, user_id):
        """Validate user exists"""
        user = User.query.get(user_id)
        return user is not None