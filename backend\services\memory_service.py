from models.database import db, Conversation, Message
import uuid
from datetime import datetime

class MemoryService:
    def __init__(self):
        self.max_messages_per_conversation = 50
    
    def create_conversation(self, user_id, title="New Conversation"):
        """Create a new conversation"""
        conversation = Conversation(
            id=str(uuid.uuid4()),
            user_id=user_id,
            title=title
        )
        db.session.add(conversation)
        db.session.commit()
        return conversation.id
    
    def add_message(self, conversation_id, role, content):
        """Add a message to conversation"""
        message = Message(
            id=str(uuid.uuid4()),
            conversation_id=conversation_id,
            role=role,
            content=content
        )
        db.session.add(message)
        
        # Limit messages per conversation
        messages = Message.query.filter_by(conversation_id=conversation_id).order_by(Message.timestamp).all()
        if len(messages) > self.max_messages_per_conversation:
            # Remove oldest message
            db.session.delete(messages[0])
        
        db.session.commit()
        return message.id
    
    def get_conversation_history(self, conversation_id, limit=20):
        """Get conversation history"""
        messages = Message.query.filter_by(conversation_id=conversation_id)\
            .order_by(Message.timestamp.desc())\
            .limit(limit)\
            .all()
        
        # Return in chronological order
        messages.reverse()
        
        return [
            {
                'role': msg.role,
                'content': msg.content,
                'timestamp': msg.timestamp.isoformat()
            }
            for msg in messages
        ]
    
    def get_user_conversations(self, user_id):
        """Get all conversations for a user"""
        conversations = Conversation.query.filter_by(user_id=user_id)\
            .order_by(Conversation.updated_at.desc())\
            .all()
        
        return [
            {
                'id': conv.id,
                'title': conv.title,
                'updated_at': conv.updated_at.isoformat()
            }
            for conv in conversations
        ]